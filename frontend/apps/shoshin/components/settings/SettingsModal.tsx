"use client"

import React from "react"
import { X, Settings } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useTheme } from "@/stores/themeStore"

interface SettingsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SettingsModal({ open, onOpenChange }: SettingsModalProps) {
  const { theme, setTheme } = useTheme()

  if (!open) return null

  const handleClose = () => onOpenChange(false)

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black/80" onClick={handleClose} />
      <div className="relative bg-white dark:bg-gray-900 border rounded-lg shadow-lg w-full max-w-md mx-4 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Settings
          </h2>
          <Button variant="ghost" size="sm" onClick={handleClose} className="h-8 w-8 p-0">
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Theme</label>
            <div className="mt-2 space-y-2">
              <button
                onClick={() => setTheme('light')}
                className={`w-full text-left px-3 py-2 rounded border ${theme === 'light' ? 'bg-blue-50 border-blue-200' : 'border-gray-200'}`}
              >
                Light
              </button>
              <button
                onClick={() => setTheme('dark')}
                className={`w-full text-left px-3 py-2 rounded border ${theme === 'dark' ? 'bg-blue-50 border-blue-200' : 'border-gray-200'}`}
              >
                Dark
              </button>
              <button
                onClick={() => setTheme('system')}
                className={`w-full text-left px-3 py-2 rounded border ${theme === 'system' ? 'bg-blue-50 border-blue-200' : 'border-gray-200'}`}
              >
                System
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
