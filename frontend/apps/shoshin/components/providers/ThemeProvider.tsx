"use client"

import { useEffect } from 'react'
import { useThemeStore } from '@/stores/themeStore'

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const initializeTheme = useThemeStore((state) => state.initializeTheme)
  const isLoading = useThemeStore((state) => state.isLoading)

  useEffect(() => {
    // Initialize theme on mount
    initializeTheme()
  }, [initializeTheme])

  // Prevent flash of unstyled content by not rendering until theme is initialized
  if (isLoading) {
    return (
      <div className="h-screen w-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return <>{children}</>
}
